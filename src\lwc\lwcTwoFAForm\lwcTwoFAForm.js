/** 
 * <AUTHOR>
 * @CreatedDate				May 14, 2024
 * @description				Two Factor Authentication Form
 * @JIRA					SFP-77761 and SFP-77721
 * @RevisionHist<PERSON><PERSON><PERSON> (Accenture), November 2024 - 2FA Revisions (callSendCodeAPI)
 */
import { LightningElement, track, api } from 'lwc';
import checkIsProduction from '@salesforce/apex/TwoFAController.checkIsProduction';
import postSendSMSCodeRequest from '@salesforce/apex/TwoFAController.postSendSMSCodeRequest';
 
export default class LwcTwoFAForm extends LightningElement {
	@api varValidAttempts;
	@api varSendSecurityCodeButton;
	@api inputSecurityCode;
	@api codeLength;
	@api varFirstAttempt;
	@api varResendSecurityCodeMessage = false;
	@api varNumberOfAttempts = 2;
	@api varTotalNumberOfAttempt = 2;
	@api varResendSecurityCodeButton = false;
	@api varGetPhoneNumber;
	@api mobileNumberLast3digits;
	@api i_understand_tickbox;
	@api I_have_read_and_understood_the_important_information;
	@api checkboxAgent_tickbox;
	@api randSecurityCode;
	@api varRandSecurityCode;
	@api securityCodeMessage = 'Before you submit this form we require you to provide a code.';

	@track errorMessage;

	async callSendCodeAPI() {
		// Generates 6 digit code
		this.varRandSecurityCode = Math.floor(100000 + Math.random() * 900000);

		// Sends SMS Code
		postSendSMSCodeRequest({
			smsPhoneNumber: this.varGetPhoneNumber,
			smsSecurityCode: this.varRandSecurityCode
		}).catch(result => {
			if(!result.isSuccess){
				this.errorMessage = result.errorMessage ?? '';
			}
		})
	}

	sendSecurityCode() {
		this.varSendSecurityCodeButton = false;
		this.varResendSecurityCodeButton = true;
		this.callSendCodeAPI();
	}

	connectedCallback(event) {
		// if(this.varTotalNumberOfAttempt == 5) {
		//     this.varTotalNumberOfAttempt = 5 - 1;
		// } else
		// if(this.varTotalNumberOfAttempt == 4) {
		//     this.varTotalNumberOfAttempt = 4 - 1;
		// } else
		if(this.i_understand_tickbox == true || this.checkboxAgent_tickbox == true) {
			if(this.varTotalNumberOfAttempt > 0) {
				this.varTotalNumberOfAttempt = this.varTotalNumberOfAttempt - 1;
			}

			// else if(this.varTotalNumberOfAttempt == 2) {
			//     this.varTotalNumberOfAttempt = 2 - 1;
			// }  else if(this.varTotalNumberOfAttempt == 1) {
			//     this.varTotalNumberOfAttempt = 1 - 1;
			// }
		}
		// if(this.varTotalNumberOfAttempt == 0) {
		//     window.location.replace("https://secure.amp.com.au/public/login/#/");
		// }
	}

	validateSecurityCode() {
		this.inputSecurityCode = this.template.querySelector('lightning-input[data-name="securityCodeText"').value;
		// this.inputSecurityCode = event.target.value;
		this.codeLength = this.inputSecurityCode.length;

		// Bypass correct 2FA code for Non-Prod environment
		checkIsProduction().then(result => {
			if(!result && this.inputSecurityCode === '000000') {
				this.inputSecurityCode = this.varRandSecurityCode;
			}
		});

		this.dispatchEvent(new FlowAttributeChangeEvent('securityCode', {
			detail: this.inputSecurityCode,
			detail: this.codeLength
		}));
	}

	resendSecurityCode() {
		var resendMessage = this.template.querySelector('.resendMessage');

		if(this.varNumberOfAttempts > 1) {
			this.varNumberOfAttempts = this.varNumberOfAttempts - 1;
			this.varResendSecurityCodeMessage = true;
			// resendMessage.innerHTML = "We have sent the security code to your mobile. You are only allowed to request this one more time.";
		} else {
			resendMessage.innerHTML = "We have sent the security code to your mobile. You cannot request this to be sent again.";
			this.varResendSecurityCodeButton = false;
		}
		this.callSendCodeAPI();
	}
}