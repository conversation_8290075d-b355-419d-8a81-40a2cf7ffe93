<!--Child component for Experian Mobile Verification
    SFP-30086 by <PERSON><PERSON><PERSON> 
-->
<template>
    <div class="slds-is-relative">
        <template if:false={isIdCheckScreen}>
            <!--display only overlay without the spinner-->
            <template if:true={processing}>
                <div class={spinnerContainerSize}></div>
            </template>
       
            <!-- Edit view -->
            <template if:true={isEditMode}>
                    <div class="slds-p-bottom_x-small">
                        <lightning-input label="Mobile" value={mobileval} type="tel" data-id="mobile" name="mobile" 
                                                    onblur={verifyNewMobileOnBlur} onchange={mobileOnchange} onfocus={onfocusEvent} field-level-help={helpText}>
                        </lightning-input>
                    </div>
            </template>

            <!-- Read view -->
            <template if:false={isEditMode}>
                <lightning-record-view-form record-id={recordId} object-api-name={objectApiName}>
                                
                        <div class="slds-grid">
                                <div class={mobileInputClass}>
                                    <label class="slds-form-element__label" for="mobile-output">Mobile</label><lightning-helptext content="At least one contact detail is required in order to create a new record. For eg Mobile or email"></lightning-helptext>
                                    <template if:true={isCallCenter}>
                                        <br/>
                                        <lightning-click-to-dial value={mobileval} record-id={recordId}></lightning-click-to-dial>
                                    </template>
                                    <template if:false={isCallCenter}>
                                        <lightning-output-field id="mobile-output" variant="label-hidden" field-name={mobileApiName} ></lightning-output-field> 
                                    </template>
                                </div>
                                <!-- SFP-32688 by Heither Ann Ballero -->
                                <template if:true={isMobileVerifyButtonVisible}>
                                    <div class="slds-col slds-size_2-of-12 slds-p-left_none slds-p-top_small slds-p-right_none">
                                        <div class="slds-is-static slds-float_right slds-grid slds-grid_align-end slds-gutters_x-small">
                                            <template if:true={canSend2FASMS}>
                                                <div class="slds-col slds-no-flex">
                                                    <lightning-button label="Send 2FA SMS" onclick={handleSend2FASMS} disabled={isSend2FADisabled} class="slds-text-nowrap"></lightning-button>
                                                </div>
                                            </template>
                                            <div class="slds-col slds-no-flex">
                                                <lightning-button label="Verify" onclick={verifyMobile} disabled={disableMobileVerifyButton} class="slds-text-nowrap"></lightning-button>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <!-- End of SFP-32688 -->
                                <div class="edit-button_visibility slds-col slds-size_1-of-12 slds-p-top_large slds-p-right_x-small">
                                    <div class= "slds-float_right">
                                        <lightning-button-icon icon-name="utility:edit" variant="bare" disabled={disableEditBtn} onclick={enableEdit} alternative-text="Edit Mobile" title="Edit Mobile" ></lightning-button-icon>
                                    </div>
                                </div>
                        </div>
                </lightning-record-view-form>
            </template>
        </template>
        <!-- SFP-32918 by Heither Ann Ballero-->
        <!-- Verify button for ID checks -->
        <template if:true={isIdCheckScreen}>
             <!-- SFP-36333 by Heither Ann Ballero -->
             <!-- Exclude NZ users from verification -->
            <template if:false={isNZUser}>
                <div class="slds-p-bottom_large">
                    <!--If verify button is visible-->
                    <template if:true={isMobileVerifyButtonForIdCheckVisible}>
                        <template if:true={processing}>
                            <div class="slds-spinner_container"></div>
                        </template>

                        <div class="slds-grid slds-gutters_x-small">
                            <template if:true={canSend2FASMS}>
                                <div class="slds-col slds-no-flex">
                                    <lightning-button label="Send 2FA SMS" onclick={handleSend2FASMS} disabled={isSend2FADisabled} class="slds-text-nowrap"></lightning-button>
                                </div>
                            </template>
                            <div class="slds-col slds-no-flex">
                                <lightning-button label="Verify" onclick={verifyMobile} disabled={disableMobileVerifyButton} class="slds-text-nowrap"></lightning-button>
                            </div>
                        </div>
                    </template>

                    <!--If there is verification message-->
                    <template if:true={verificationMessage}>
                        <p class={verificationStyle}>
                            <lightning-icon icon-name={verificationIconName} variant={verificationVariant} alternative-text={verificationMessage} title={verificationMessage} size="xx-small"></lightning-icon>
                            &nbsp; {verificationMessage} {dateDetails}
                        </p>
                    </template>
                
                </div>
            </template>
        </template>

        <!-- 2FA SMS Modal -->
        <template if:true={isModalOpen}>
            <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container">
                    <header class="slds-modal__header">
                        <lightning-button-icon class="slds-modal__close" title="Close" onclick={closeModal} icon-name="utility:close" icon-class="slds-button_icon-inverse" variant="bare-inverse"></lightning-button-icon>
                        <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">Send 2FA SMS</h2>
                    </header>
                    <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">

                        <!-- Include the lwcTwoFAForm component -->
                        <c-lwc-two-f-a-form
                            var-valid-attempts={twoFAValidAttempts}
                            var-send-security-code-button={twoFASendSecurityCodeButton}
                            input-security-code={twoFAInputSecurityCode}
                            code-length={twoFACodeLength}
                            var-first-attempt={twoFAFirstAttempt}
                            var-resend-security-code-message={twoFAResendSecurityCodeMessage}
                            var-number-of-attempts={twoFANumberOfAttempts}
                            var-total-number-of-attempt={twoFATotalNumberOfAttempt}
                            var-resend-security-code-button={twoFAResendSecurityCodeButton}
                            var-get-phone-number={mobileval}
                            mobile-number-last3digits={mobileNumberLast3Digits}
                            i-understand-tickbox={twoFAIUnderstandTickbox}
                            i-have-read-and-understood-the-important-information={twoFAIHaveReadAndUnderstood}
                            checkbox-agent-tickbox={twoFACheckboxAgentTickbox}
                            rand-security-code={twoFARandSecurityCode}
                            var-rand-security-code={twoFAVarRandSecurityCode}
                            ontwofacodevalidated={handleTwoFACodeValidated}
                            ontwofacodesent={handleTwoFACodeSent}>
                        </c-lwc-two-f-a-form>
                    </div>
                    <footer class="slds-modal__footer">
                        <lightning-button variant="neutral" label="Cancel" onclick={closeModal} class="slds-m-right_small"></lightning-button>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </template>
   </div>
</template>